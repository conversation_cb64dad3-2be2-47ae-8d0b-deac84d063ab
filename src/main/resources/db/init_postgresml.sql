-- PostgresML 向量数据库初始化脚本
-- 用于设置 pgvector 扩展和创建向量表
-- 支持 BAAI/bge-m3 模型（1024维向量）

-- 0. 清理旧表和函数（解决维度不匹配问题）
DROP TABLE IF EXISTS embeddings CASCADE;
DROP FUNCTION IF EXISTS search_embeddings CASCADE;
DROP FUNCTION IF EXISTS add_embedding CASCADE;

-- 1. 创建 pgvector 扩展（如果还没有安装）
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. 创建向量存储表（1024维向量）
CREATE TABLE IF NOT EXISTS embeddings (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(1024), -- BAAI/bge-m3 的向量维度是 1024
    file_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX ON math_rag_knowledge USING hnsw (embedding vector_cosine_ops);


-- 3. 创建向量相似度搜索索引
-- 使用 IVFFlat 索引提高查询性能
CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- 4. 创建元数据索引
CREATE INDEX IF NOT EXISTS embeddings_metadata_idx 
ON embeddings USING GIN (metadata);

-- 5. 创建文件名索引
CREATE INDEX IF NOT EXISTS embeddings_file_name_idx 
ON embeddings (file_name);

-- 6. 创建相似度搜索函数（1024维）
CREATE OR REPLACE FUNCTION search_embeddings(
    query_embedding vector(1024),
    match_threshold float DEFAULT 0.75,
    match_count int DEFAULT 5
)
RETURNS TABLE(
    id int,
    content text,
    metadata jsonb,
    file_name varchar(255),
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.content,
        e.metadata,
        e.file_name,
        1 - (e.embedding <=> query_embedding) AS similarity
    FROM embeddings e
    WHERE 1 - (e.embedding <=> query_embedding) > match_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 7. 创建添加向量的函数（1024维）
CREATE OR REPLACE FUNCTION add_embedding(
    p_content text,
    p_embedding vector(1024),
    p_metadata jsonb DEFAULT NULL,
    p_file_name varchar(255) DEFAULT NULL
)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
    new_id int;
BEGIN
    INSERT INTO embeddings (content, embedding, metadata, file_name)
    VALUES (p_content, p_embedding, p_metadata, p_file_name)
    RETURNING id INTO new_id;
    
    RETURN new_id;
END;
$$;

-- 8. 创建更新时间戳触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_embeddings_updated_at 
    BEFORE UPDATE ON embeddings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 9. 验证配置信息
SELECT 'PostgresML 向量数据库初始化完成' as status;
SELECT 'BAAI/bge-m3 模型支持' as model;
SELECT '向量维度: 1024' as dimension;

-- 显示表结构和索引信息
\d embeddings
\di embeddings*; 