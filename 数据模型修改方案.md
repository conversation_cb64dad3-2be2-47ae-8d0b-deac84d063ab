# 1️⃣ 确定统一数据结构

无论你的原始数据是 PDF、Markdown、网页还是爬下来的题库，统一存入数据库前都要 **抽象成统一字段**：

| 字段       | 类型          | 说明                                       |
| ---------- | ------------- | ------------------------------------------ |
| id         | SERIAL / UUID | 唯一标识                                   |
| source     | TEXT          | 数据来源（PDF 名称、网页 URL 等）          |
| content    | TEXT          | 文本内容（去掉排版，只保留可读文本）       |
| type       | TEXT          | 内容类型（“题目/讲解/概念/公式/例题”）     |
| grade      | TEXT          | 年级（P5, Sec2, O-Level 等）               |
| topic      | TEXT          | 题型/知识点（代数、几何、数列、函数等）    |
| difficulty | TEXT          | 简单/中等/难                               |
| metadata   | JSONB         | 额外信息（原始文件页码、章节、考试年份等） |
| embedding  | vector        | 向量表示（用于检索）                       |

> 核心思想：**不管原始格式如何，最终都是“文本 + 标签 + embedding + metadata”**。

------

# 2️⃣ 数据清洗流程

1. **PDF** → OCR / PDF parser → 文本
2. **Markdown** → strip Markdown → 纯文本
3. **网页** → 网页解析器（如 BeautifulSoup）→ 去掉广告/导航 → 纯文本
4. **统一文本格式**：
   - 移除换行/空格冗余
   - 保留公式和题干/答案对应关系（可用标记符 `Q:`、`A:`）
5. **分块切片**：
   - 题目型：一题一条记录（题干 + 分步答案一起入库）
   - 概念型：按最小知识点切片（例如“二次函数定义”、“等差数列通项公式”）

------

# 3️⃣ 知识库表设计建议（pgvector）

```
CREATE TABLE math_rag_knowledge (
    id SERIAL PRIMARY KEY,
    source TEXT,
    content TEXT,
    type TEXT,
    grade TEXT,
    topic TEXT,
    difficulty TEXT,
    metadata JSONB,
    embedding vector(1536)  -- 与你embedding模型维度一致
);
CREATE INDEX ON math_rag_knowledge USING hnsw (embedding vector_cosine_ops);
```

**思路**：

- **metadata** 可以存放多维信息（章节/页码/年份/公式标记等），方便检索过滤
- **embedding** 对应文本内容的向量表示，用于 RAG 检索

------

# 4️⃣ 资源整合策略

1. **统一入口**：
   - 建立爬虫 + PDF + Markdown 处理 pipeline → 输出到统一 JSON / CSV
2. **分层存储**：
   - **题目层**：题干 + 分步解答
   - **概念层**：知识点 + 定义 + 示例
   - **文档层**：教材章节/网页文章原文
3. **统一切分 + 标注**：
   - 每条记录都必须有 type / topic / grade / source
   - 对题目，content = “Q: … A: …”，embedding 用题干 + 答案合并文本
4. **向量化 + 入库**：
   - 调用 embedding 模型生成向量 → 插入 pgvector

------

# 5️⃣ 检索策略

- **向量检索** → 找最相关文档
- **metadata 过滤** → 限制年级、题型、难度
- **混合检索** → 向量 + 关键词（例如题目关键词、公式符号）

示例 SQL：

```
SELECT id, content, metadata
FROM math_rag_knowledge
WHERE grade='Sec2' AND topic='Algebra'
ORDER BY embedding <=> '[query_embedding]'::vector
LIMIT 5;
```

------

# 6️⃣ 总结核心原则

1. **统一文本格式** → 任何原始数据都要抽象成同一结构
2. **小单元切片** → 题目/概念/讲解分条存储
3. **丰富 metadata** → 检索准确度 + 分级/难度过滤
4. **向量化** → embedding + pgvector HNSW 索引
5. **RAG 检索** → 先检索 → 再给 Gemini 生成输出

> 💡 核心就是“数据统一 + 分块 + 标签 + embedding”，剩下的交给 RAG + Gemini 就行了。